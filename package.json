{"name": "<PERSON>k<PERSON><PERSON>", "version": "1.2.0", "description": "A simplistic and purely anonymous chat app", "type": "module", "scripts": {"dev": "bun run --watch src/server.ts", "test": "bun run src/server.ts & bun test --watch", "start": "bun run src/server.ts"}, "dependencies": {"@debutter/trufflebyte": "npm:@jsr/debutter__trufflebyte@1.4.1", "@hono/zod-openapi": "^0.19.9", "@libsql/kysely-libsql": "^0.4.1", "@scalar/hono-api-reference": "^0.8.10", "bcrypt": "^5.1.1", "chalk": "^5.4.1", "hono": "^4.8.3", "hono-rate-limiter": "^0.4.2", "jose": "^6.0.10", "kysely": "^0.27.6", "openpgp": "^6.1.1", "zod": "^3.25.67"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bun": "^1.2.9", "@types/node": "^22.14.1", "bun-types": "latest"}}