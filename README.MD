# WonkChat

Just another simplistic and purely anonymous chat app for wonky fellows to chat in.

## Features

-   **Anonymity**: Chat with others without revealing your identity.
-   **Simplicity**: A straightforward interface for easy communication.
-   **Real-time**: Instantly send and receive messages.

## API Documentation

Documentation for the WonkChat's homeserver API can be found [here](https://wonk-docs.debutter.dev/).
Additionally, you can find and edit the documentation in the [`docs/`](docs) folder within this repository.

## Getting Started

If you wish to install and run WonkChat, follow these steps:

```sh
git clone https://github.com/ButterDebugger/WonkChat.git
cd WonkChat
npm install
npm start
```

## License

WonkChat is licensed under [`MIT License`](LICENSE).
