html {
	height: 100%;
}
body {
	height: 100%;
}
*,
*:before,
*:after {
	box-sizing: border-box;
}

#root {
	display: flex;

	width: 100%;
	height: 100%;
}
main {
	display: flex;
	flex-direction: column;
	gap: 8px;
	flex: 1;

	padding: 48px 36px;
	max-width: 480px;
	margin: auto;
}

/* Input styles */
input[type="text"],
input[type="password"],
button,
textarea,
select {
	border-radius: 16px;
	padding: 8px 12px;
}
input[type="text"],
input[type="password"],
textarea {
	font-size: 17px;
	width: var(--fill-available);
}
button {
	padding: 8px 24px;
}

/* Generic styles */
.header {
	align-self: center;

	font-size: 30px;
	margin: 0px 0px 16px;
}
.subheader {
	margin: 0px 0px 10px;
	font-size: 13px;
	text-align: center;
}
.subheader:empty {
	display: none;
}

#error-message {
	color: orangered;
}

.input-container {
	display: flex;
	flex-direction: column;
	gap: 4px;
}
.input-container.row {
	flex-direction: row-reverse;
	justify-content: start;
	align-items: center;
	gap: 8px;
}
.field-group {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	gap: 8px;
}
.field-group.stretch > *:first-child {
	flex: 1;
}
.field-label {
	font-size: 14px;
	font-weight: 700;
	line-height: 1.2lh;
}

#trusted-origin {
	font-style: italic;
	font-weight: normal;
	padding: 0px 0.5ch;
}
#trusted-origin.error {
	font-style: normal;
	color: orangered;
}

form {
	display: flex;
	flex-direction: column;
	gap: 16px;
}
#submit {
	align-self: flex-end;
}
